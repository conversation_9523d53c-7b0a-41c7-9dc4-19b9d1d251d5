{
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features"
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[javascript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "files.associations": {
    "betayun-Dockerfile": "dockerfile",
    "Dockerfile-betayun": "dockerfile"
  },
  "liveServer.settings.port": 0,
  "git.suggestSmartCommit": false,
  "eslint.enable": true,
  "[typescriptreact]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  },
  "[typescript]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "vscode.typescript-language-features",
  },
  "js/ts.implicitProjectConfig.experimentalDecorators": true,
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "eslint.format.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "never"
  },
  "editor.formatOnSave": true,
  "terminal.integrated.scrollback": 5000,
  "json.maxItemsComputed": 1000,
  "[javascriptreact]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "[css]": {
    "editor.defaultFormatter": "vscode.css-language-features"
  },
  "github.copilot.enable": {
    "*": true,
    "yaml": true,
    "plaintext": false,
    "markdown": false
  },
  "editor.inlineSuggest.enabled": true,
  "window.zoomLevel": 0,
  // 插件配置项
  "fileheader.configObj": {
    "autoAdd": true, // 检测文件没有头部注释，自动添加文件头部注释
    "autoAlready": true, // 只添加插件支持的语言以及用户通过`language`选项自定义的注释
    "supportAutoLanguage": [], // 设置之后，在数组内的文件才支持自动添加
    // 自动添加头部注释黑名单
    "prohibitAutoAdd": [
      "json"
    ],
    "prohibitItemAutoAdd": [
      "项目的全称禁止项目自动添加头部注释, 使用快捷键自行添加"
    ],
    "folderBlacklist": [
      "node_modules"
    ], // 文件夹或文件名禁止自动添加头部注释
    "wideSame": false, // 头部注释等宽设置
    "wideNum": 13, // 头部注释字段长度 默认为13
    "functionWideNum": 0, // 函数注释等宽设置 设为0 即为关闭
    "switch": {
      "newlineAddAnnotation": true // 默认遇到换行符(\r\n \n \r)添加注释符号
    },
    "moveCursor": true, // 自动移动光标到Description所在行
    "dateFormat": "YYYY-MM-DD HH:mm:ss",
    "createHeader": true, // 新建文件自动添加头部注释
    // 自定义配置是否在函数内生成注释 不同文件类型和语言类型
    "openFunctionParamsCheck": true, // 开启关闭自动提取添加函数参数
    "functionParamsShape": [
      "{",
      "}"
    ], // 函数参数外形自定义 
    // "functionParamsShape": "no type" 函数参数不需要类型
    "functionTypeSymbol": "", // 参数没有类型时的默认值
    "typeParamOrder": "type param", // 参数类型 和 参数的位置自定义
    // 自定义语言注释，自定义取消 head、end 部分
    "throttleTime": 60000, // 对同一个文件 需要过1分钟再次修改文件并保存才会更新注释
    // 自定义语言注释符号，覆盖插件的注释格式
    "language": {
      // js后缀文件
      "js": {
        "head": "/**",
        "middle": " * @",
        "end": " */",
        "functionParams": "typescript" // 函数注释使用ts语言的解析逻辑
      },
      // 一次匹配多种文件后缀文件 不用重复设置
      "h/hpp/cpp": {
        "head": "/*** ", // 统一增加几个*号
        "middle": " * @",
        "end": " */"
      },
      // 针对有特殊要求的文件如：test.blade.php
      "blade.php": {
        "head": "<!--",
        "middle": " * @",
        "end": "-->",
      }
    },
    // 默认注释  没有匹配到注释符号的时候使用。
    "annotationStr": {
      "head": "/**",
      "middle": " * @",
      "end": " */",
      "use": false
    },
  },
  // 头部注释模版
  "fileheader.customMade": {
    "Description": "", // 介绍文件的作用、文件的入参、出参。
    // Author字段是文件的创建者 可以在specialOptions中更改特殊属性
    // 公司项目和个人项目可以配置不同的用户名与邮箱 搜索: gitconfig includeIf  比如: https://ayase.moe/2021/03/09/customized-git-config/
    // 自动提取当前git config中的: 用户名、邮箱
    "Author": "git config user.name && git config user.email", // 同时获取用户名与邮箱
    // "Author": "git config user.name", // 仅获取用户名
    // "Author": "git config user.email", // 仅获取邮箱
    // "Author": "OBKoro1", // 写死的固定值 不从git config中获取
    "Date": "Do not edit", // 文件创建时间(不变)
  },
  // 函数注释模版
  "fileheader.cursorMode": {
    "description": "", // 函数注释生成之后，光标移动到这里
    "param": "", // param 开启函数参数自动提取 需要将光标放在函数行或者函数上方的空白行
    "returns": "",
  },
  "workbench.colorCustomizations": {}
}